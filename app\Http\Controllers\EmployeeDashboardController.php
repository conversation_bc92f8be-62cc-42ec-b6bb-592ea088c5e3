<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;
use App\Models\EmployeeShift;

class EmployeeDashboardController extends Controller
{
    /**
     * Display the employee's main dashboard.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $employeeProfile = $user->employeeProfile;

        if (!$employeeProfile) {
            if ($user->hasRole(['Super Admin', 'Admin'])) {
                return redirect()->route('admin.dashboard');
            }
            Auth::logout();
            return redirect('/login')->with('error', 'Your employee profile is not set up.');
        }

        $year = $request->input('year', now()->year);
        $month = $request->input('month', now()->month);
        $displayDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();
        
        $monthStart = $displayDate->copy()->startOfMonth();
        $monthEnd = $displayDate->copy()->endOfMonth();

        $todaysAssignment = EmployeeShift::with(['shift.policy', 'workZone'])
            ->where('employee_profile_id', $employeeProfile->id)
            ->whereDate('start_date', '<=', today())
            ->where(fn ($q) => $q->whereDate('end_date', '>=', today())->orWhereNull('end_date'))
            ->first();

        $currentAttendance = $employeeProfile->attendances()->whereDate('check_in_time', today())->whereNull('check_out_time')->first();
        
        $baseMonthlyQuery = $employeeProfile->attendances()->whereBetween('check_in_time', [$monthStart, $monthEnd]);

        $monthlyAttendances = (clone $baseMonthlyQuery)->with('shift')->get();

        // --- NEW SCHEDULED HOURS CALCULATION ---
        $assignedShiftsInMonth = EmployeeShift::where('employee_profile_id', $employeeProfile->id)
            ->where(function ($query) use ($monthStart, $monthEnd) {
                $query->where('start_date', '<=', $monthEnd)
                    ->where(fn($q) => $q->where('end_date', '>=', $monthStart)->orWhereNull('end_date'));
            })
            ->with('shift')
            ->get();
        
        $totalScheduledMinutes = 0;
        // We iterate from the start to the end of the month to count working days
        for ($day = $monthStart->copy(); $day->lte($monthEnd); $day->addDay()) {
            if ($day->isWeekend()) continue;
            
            // Find which shift assignment was active on this specific day
            $activeAssignment = $assignedShiftsInMonth->first(function ($assignment) use ($day) {
                $starts = Carbon::parse($assignment->start_date);
                $ends = $assignment->end_date ? Carbon::parse($assignment->end_date) : null;
                return $day->between($starts, $ends ?? now()->addYear());
            });

            if ($activeAssignment && $activeAssignment->shift) {
                $start = Carbon::parse($activeAssignment->shift->start_time);
                $end = Carbon::parse($activeAssignment->shift->end_time);
                if ($activeAssignment->shift->spans_two_days) $end->addDay();
                $totalScheduledMinutes += $start->diffInMinutes($end) - $activeAssignment->shift->unpaid_break_minutes;
            }
        }
        
        $totalOvertime = (clone $baseMonthlyQuery)->sum('overtime_minutes');
        $totalUndertime = (clone $baseMonthlyQuery)->sum('undertime_minutes');
        $daysLate = $monthlyAttendances->where('status', 'late')->count();

        $onTimeDays = $monthlyAttendances->where('status', 'on_time')->count();

        $totalWorkedMinutes = $monthlyAttendances->sum(function ($att) {
            if (!$att->check_out_time || !$att->shift) return 0;
            return Carbon::parse($att->check_in_time)->diffInMinutes(Carbon::parse($att->check_out_time)) - $att->shift->unpaid_break_minutes;
        });
        $totalRegularMinutes = $totalWorkedMinutes - $totalOvertime;

        // ### THE FIX: The weekly activity is now relative to the displayed month ###
        $weeklyQueryStart = $displayDate->copy()->endOfMonth()->subDays(6)->startOfDay();
        $weeklyQueryEnd = $displayDate->copy()->endOfMonth()->endOfDay();

        $weeklyActivityData = $employeeProfile->attendances()
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->orderBy('check_in_time')
            ->get()
            ->groupBy(fn($date) => Carbon::parse($date->check_in_time)->format('D'))
            ->mapWithKeys(function ($day, $dayName) {
                $totalMinutes = $day->sum(function ($att) {
                    if (!$att->check_out_time) return 0;
                    return Carbon::parse($att->check_in_time)->diffInMinutes(Carbon::parse($att->check_out_time));
                });
                // Use mapWithKeys to ensure the day name is the key
                return [$dayName => round($totalMinutes / 60, 2)];
            });

        return Inertia::render('Dashboard', [
            'todaysAssignment' => $todaysAssignment,
            'currentAttendance' => $currentAttendance,
            'monthlyStats' => [
                'total_overtime_minutes' => (int) $totalOvertime,
                'total_undertime_minutes' => (int) $totalUndertime,
                'days_late' => $daysLate,
                'total_scheduled_minutes' => $totalScheduledMinutes,
                'on_time_days' => $onTimeDays,
                'total_regular_minutes' => ($totalScheduledMinutes - $totalUndertime),
            ],
            'recentActivity' => $employeeProfile->attendances()->orderBy('check_in_time', 'desc')->take(5)->get(),
            'weeklyActivityData' => $weeklyActivityData, // Pass the correct data
            'displayMonth' => $displayDate->format('F Y'),
        ]);
    }
}