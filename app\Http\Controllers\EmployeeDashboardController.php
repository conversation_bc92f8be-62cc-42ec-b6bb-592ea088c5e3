<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;
use App\Models\Attendance;
use App\Models\EmployeeProfile;
use App\Models\EmployeeShift;

class EmployeeDashboardController extends Controller
{
    /**
     * Display the employee's main dashboard.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $employeeProfile = $user->employeeProfile;

        if (!$employeeProfile) {
            if ($user->hasRole(['Super Admin', 'Admin'])) {
                return redirect()->route('admin.dashboard');
            }
            Auth::logout();
            return redirect('/login')->with('error', 'Your employee profile is not set up.');
        }

        $year = $request->input('year', now()->year);
        $month = $request->input('month', now()->month);
        $displayDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();
        
        $monthStart = $displayDate->copy()->startOfMonth();
        $monthEnd = $displayDate->copy()->endOfMonth();

        $todaysAssignment = EmployeeShift::with(['shift.policy', 'workZone'])
            ->where('employee_profile_id', $employeeProfile->id)
            ->whereDate('start_date', '<=', today())
            ->where(fn ($q) => $q->whereDate('end_date', '>=', today())->orWhereNull('end_date'))
            ->first();

        $currentAttendance = $employeeProfile->attendances()->whereDate('check_in_time', today())->whereNull('check_out_time')->first();
        
        $baseMonthlyQuery = $employeeProfile->attendances()->whereBetween('check_in_time', [$monthStart, $monthEnd]);

        $monthlyAttendances = (clone $baseMonthlyQuery)->with('shift')->get();

        // --- NEW SCHEDULED HOURS CALCULATION ---
        $assignedShiftsInMonth = EmployeeShift::where('employee_profile_id', $employeeProfile->id)
            ->where(function ($query) use ($monthStart, $monthEnd) {
                $query->where('start_date', '<=', $monthEnd)
                    ->where(fn($q) => $q->where('end_date', '>=', $monthStart)->orWhereNull('end_date'));
            })
            ->with('shift')
            ->get();
        
        $totalScheduledMinutes = 0;
        // We iterate from the start to the end of the month to count working days
        for ($day = $monthStart->copy(); $day->lte($monthEnd); $day->addDay()) {
            if ($day->isWeekend()) continue;
            
            // Find which shift assignment was active on this specific day
            $activeAssignment = $assignedShiftsInMonth->first(function ($assignment) use ($day) {
                $starts = Carbon::parse($assignment->start_date);
                $ends = $assignment->end_date ? Carbon::parse($assignment->end_date) : null;
                return $day->between($starts, $ends ?? now()->addYear());
            });

            if ($activeAssignment && $activeAssignment->shift) {
                $start = Carbon::parse($activeAssignment->shift->start_time);
                $end = Carbon::parse($activeAssignment->shift->end_time);
                if ($activeAssignment->shift->spans_two_days) $end->addDay();
                $totalScheduledMinutes += $start->diffInMinutes($end) - $activeAssignment->shift->unpaid_break_minutes;
            }
        }
        
        $totalOvertime = (clone $baseMonthlyQuery)->sum('overtime_minutes');
        $totalUndertime = (clone $baseMonthlyQuery)->sum('undertime_minutes');
        $daysLate = $monthlyAttendances->where('status', 'late')->count();

        $onTimeDays = $monthlyAttendances->where('status', 'on_time')->count();

        // Calculate absent days and early days for enhanced stats
        $totalWorkingDaysInMonth = 0;
        for ($day = $monthStart->copy(); $day->lte($monthEnd); $day->addDay()) {
            if (!$day->isWeekend()) $totalWorkingDaysInMonth++;
        }

        $totalAttendanceDays = $monthlyAttendances->count();
        $absentDays = max(0, $totalWorkingDaysInMonth - $totalAttendanceDays);

        // Count early checkout days (attendances with undertime > 0)
        $earlyDays = $monthlyAttendances->where('undertime_minutes', '>', 0)->count();

        $totalWorkedMinutes = $monthlyAttendances->sum(function ($att) {
            if (!$att->check_out_time || !$att->shift) return 0;
            return Carbon::parse($att->check_in_time)->diffInMinutes(Carbon::parse($att->check_out_time)) - $att->shift->unpaid_break_minutes;
        });
        $totalRegularMinutes = $totalWorkedMinutes - $totalOvertime;

        // ### IMPROVED: Dynamic 7-day activity relative to the displayed month ###
        // Get the last 7 days of the displayed month (or last 7 days if current month)
        if ($displayDate->isCurrentMonth()) {
            // If viewing current month, show last 7 days from today
            $weeklyQueryStart = now()->subDays(6)->startOfDay();
            $weeklyQueryEnd = now()->endOfDay();
        } else {
            // If viewing past month, show last 7 days of that month
            $weeklyQueryEnd = $displayDate->copy()->endOfMonth()->endOfDay();
            $weeklyQueryStart = $weeklyQueryEnd->copy()->subDays(6)->startOfDay();
        }

        // Get employee's shift assignments for the last 7 days to determine working days
        $weeklyShiftAssignments = EmployeeShift::where('employee_profile_id', $employeeProfile->id)
            ->where(function ($query) use ($weeklyQueryStart, $weeklyQueryEnd) {
                $query->where('start_date', '<=', $weeklyQueryEnd->toDateString())
                    ->where(fn($q) => $q->where('end_date', '>=', $weeklyQueryStart->toDateString())->orWhereNull('end_date'));
            })
            ->with('shift')
            ->get();

        // Determine which days the employee should be working
        $workingDays = [];
        for ($day = $weeklyQueryStart->copy(); $day->lte($weeklyQueryEnd); $day->addDay()) {
            $dayName = $day->format('D');

            // Check if employee has a shift assignment for this day
            $hasShiftAssignment = $weeklyShiftAssignments->first(function ($assignment) use ($day) {
                $starts = Carbon::parse($assignment->start_date);
                $ends = $assignment->end_date ? Carbon::parse($assignment->end_date) : null;
                return $day->between($starts, $ends ?? now()->addYear());
            });

            // Include weekdays by default, weekends only if there's a shift assignment
            if (!$day->isWeekend() || $hasShiftAssignment) {
                $workingDays[] = $dayName;
            }
        }

        $weeklyActivityData = $employeeProfile->attendances()
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->orderBy('check_in_time', 'desc')
            ->get()
            ->groupBy(function ($attendance) {
                return $attendance->check_in_time->format('Y-m-d');
            })
            ->map(function ($dayAttendances) {
                // Get the latest attendance for each day
                $latestAttendance = $dayAttendances->first();

                // Only count completed attendances (with check_out_time)
                if (!$latestAttendance->check_out_time) {
                    return 0; // Don't count incomplete attendances
                }

                $minutes = $latestAttendance->check_in_time->diffInMinutes($latestAttendance->check_out_time);
                return round($minutes / 60, 2);
            })
            ->mapWithKeys(function ($hours, $date) {
                $dayName = Carbon::parse($date)->format('D');
                return [$dayName => $hours];
            });

        // Only include working days in the final data
        $filteredWeeklyData = [];
        foreach ($workingDays as $day) {
            $filteredWeeklyData[$day] = $weeklyActivityData[$day] ?? 0;
        }

        $weeklyActivityData = $filteredWeeklyData;

        // Get incomplete attendances for the last 7 days
        $incompleteAttendances = $employeeProfile->attendances()
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->whereNull('check_out_time')
            ->get()
            ->groupBy(fn($date) => Carbon::parse($date->check_in_time)->format('D'))
            ->mapWithKeys(function ($day, $dayName) {
                return [$dayName => $day->count()];
            });

        return Inertia::render('Dashboard', [
            'todaysAssignment' => $todaysAssignment,
            'currentAttendance' => $currentAttendance,
            'monthlyStats' => [
                'total_overtime_minutes' => (int) $totalOvertime,
                'total_undertime_minutes' => (int) $totalUndertime,
                'days_late' => $daysLate,
                'total_scheduled_minutes' => $totalScheduledMinutes,
                'on_time_days' => $onTimeDays,
                'total_regular_minutes' => ($totalScheduledMinutes - $totalUndertime),
                'absent_days' => $absentDays,
                'early_days' => $earlyDays,
                'total_working_days' => $totalWorkingDaysInMonth,
            ],
            'recentActivity' => $employeeProfile->attendances()
                ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
                ->orderBy('check_in_time', 'desc')
                ->get()
                ->groupBy(function ($attendance) {
                    return $attendance->check_in_time->format('Y-m-d');
                })
                ->map(function ($dayAttendances) {
                    // Return the latest attendance for each day
                    return $dayAttendances->first();
                })
                ->values()
                ->take(7), // Show up to 7 days
            'weeklyActivityData' => $weeklyActivityData, // Pass the correct data
            'incompleteAttendances' => $incompleteAttendances, // Pass incomplete attendance data
            'displayMonth' => $displayDate->format('F Y'),
        ]);
    }
}