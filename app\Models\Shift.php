<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Shift extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'start_time',
        'end_time',
        'unpaid_break_minutes',
    ];

    /**
     * Get the policy associated with the shift.
     */
    public function policy(): HasOne
    {
        return $this->hasOne(ShiftPolicy::class);
    }
}