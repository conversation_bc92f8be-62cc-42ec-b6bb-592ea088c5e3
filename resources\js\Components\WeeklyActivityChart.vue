<script setup>
import { ref, watchEffect } from 'vue';
import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';
import { useResizeObserver, useDebounceFn } from '@vueuse/core';

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);
const props = defineProps({ activityData: Object });
const chartData = ref({});
const chartRef = ref(null);
const containerRef = ref(null);

watchEffect(() => {
    const labels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const data = labels.map(day => props.activityData[day] || 0);
    chartData.value = {
        labels,
        datasets: [{ label: 'Hours Worked', backgroundColor: '#8884d8', borderRadius: 4, data }],
    };
});

// ### THE FIX: Debounced Resize Handler ###
// This creates a new function that will only run after 100ms of no activity.
const debouncedResize = useDebounceFn(() => {
    if (chartRef.value?.chart) {
        chartRef.value.chart.resize();
    }
}, 100);

useResizeObserver(containerRef, debouncedResize);

const chartOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true }, x: { grid: { display: false } } } };
</script>

<template>
    <div ref="containerRef" class="w-full h-full relative">
        <Bar ref="chartRef" :data="chartData" :options="chartOptions" />
    </div>
</template>