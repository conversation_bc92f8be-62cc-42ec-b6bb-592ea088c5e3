<script setup>
import { ref, watchEffect } from 'vue';
import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';
import { useResizeObserver, useDebounceFn } from '@vueuse/core';

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);
const props = defineProps({
    activityData: Object,
    incompleteAttendances: Object
});
const chartData = ref({});
const chartRef = ref(null);
const containerRef = ref(null);

watchEffect(() => {
    // Only show days that are present in the activity data (working days)
    const labels = Object.keys(props.activityData || {});
    const completedData = labels.map(day => props.activityData?.[day] || 0);

    // Create background colors based on whether there are incomplete attendances
    const backgroundColors = labels.map(day => {
        const hasIncomplete = (props.incompleteAttendances?.[day] || 0) > 0;
        return hasIncomplete ? '#f59e0b' : '#8884d8'; // Orange for incomplete, blue for complete
    });

    chartData.value = {
        labels,
        datasets: [{
            label: 'Hours Worked',
            backgroundColor: backgroundColors,
            borderRadius: 4,
            data: completedData,
            barThickness: 20,
            maxBarThickness: 30
        }],
    };
});

// ### THE FIX: Debounced Resize Handler ###
// This creates a new function that will only run after 100ms of no activity.
const debouncedResize = useDebounceFn(() => {
    if (chartRef.value?.chart) {
        chartRef.value.chart.resize();
    }
}, 100);

useResizeObserver(containerRef, debouncedResize);

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: { display: false },
        tooltip: {
            callbacks: {
                label: function(context) {
                    const day = context.label;
                    const hours = context.raw;
                    const incomplete = props.incompleteAttendances?.[day] || 0;

                    let label = `${hours} hours completed`;
                    if (incomplete > 0) {
                        label += ` (${incomplete} incomplete attendance${incomplete > 1 ? 's' : ''})`;
                    }
                    return label;
                }
            }
        }
    },
    scales: {
        y: {
            beginAtZero: true,
            ticks: {
                callback: function(value) {
                    return value + 'h';
                }
            }
        },
        x: {
            grid: { display: false }
        }
    }
};
</script>

<template>
    <div ref="containerRef" class="w-full h-full relative">
        <Bar ref="chartRef" :data="chartData" :options="chartOptions" />
    </div>
</template>