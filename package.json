{"private": true, "type": "module", "scripts": {"build": "vite build && vite build --ssr && vite build && vite build --ssr --ssr && vite build && vite build --ssr && vite build && vite build --ssr --ssr --ssr", "build:ssr": "vite build && vite build --ssr && vite build && vite build --ssr --ssr && vite build && vite build --ssr && vite build && vite build --ssr --ssr --ssr && vite build && vite build --ssr && vite build && vite build --ssr --ssr && vite build && vite build --ssr && vite build && vite build --ssr --ssr --ssr --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@inertiajs/vue3": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@types/node": "^22.13.5", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/server-renderer": "^3.4.0", "autoprefixer": "^10.4.12", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "postcss": "^8.4.31", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.2.1", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "^8.23.0", "vue": "^3.4.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/vite": "^4.1.1", "@tanstack/vue-table": "^8.21.3", "@vitejs/plugin-vue": "^5.2.1", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vueuse/core": "^12.8.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "leaflet": "^1.9.4", "leaflet-geosearch": "^4.2.0", "lucide-vue-next": "^0.468.0", "radix-vue": "^1.9.17", "reka-ui": "^2.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.3.4", "typescript": "^5.2.2", "vite": "^6.2.0", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-sonner": "^2.0.0", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}