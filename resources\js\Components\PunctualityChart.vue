<script setup>
import { Doughnut } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, ArcElement, CategoryScale } from 'chart.js';
import { computed } from 'vue';

ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale);

const props = defineProps({
    stats: Object,
});

const chartData = computed(() => {
    return {
        labels: ['On Time', 'Late'],
        datasets: [{
            backgroundColor: ['#4ade80', '#f87171'], // Green for on-time, Red for late
            data: [props.stats?.on_time_days || 0, props.stats?.days_late || 0],
            borderWidth: 0,
        }],
    };
});

const onTimePercentage = computed(() => {
    const onTime = props.stats?.on_time_days || 0;
    const late = props.stats?.days_late || 0;
    const totalDays = onTime + late;
    if (totalDays === 0) return 100;
    return Math.round((onTime / totalDays) * 100);
});

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '80%',
    plugins: {
        legend: { display: false },
        tooltip: {
            callbacks: {
                label: function(context) {
                    let label = context.dataset.label || '';
                    if (context.parsed !== null) {
                        label += `: ${context.label} (${context.raw} days)`;
                    }
                    return label;
                }
            }
        }
    },
};
</script>

<template>
     <div class="relative w-full h-full flex items-center justify-center">
        <Doughnut :data="chartData" :options="chartOptions" />
         <div class="absolute flex flex-col items-center justify-center pointer-events-none">
            <span class="text-3xl font-bold">{{ onTimePercentage }}%</span>
            <span class="text-xs text-muted-foreground">Punctual</span>
        </div>
    </div>
</template>