<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\EmployeeProfile;
use App\Models\Shift;
use App\Models\ShiftPolicy;
use App\Models\WorkZone;
use App\Models\EmployeeShift;
use App\Models\Attendance;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call(RolesAndPermissionsSeeder::class);

        // Create Admin and necessary shifts/zones first
        $superAdminUser = User::factory()->create(['name' => 'Admin User', 'email' => '<EMAIL>', 'password' => Hash::make('password')]);
        EmployeeProfile::factory()->create(['user_id' => $superAdminUser->id, 'first_name' => 'Admin', 'last_name' => 'User', 'employee_id_number' => 'EMP0000']);
        $superAdminUser->assignRole('Super Admin');
        
        $dayShift = Shift::factory()->dayShift()->create(['unpaid_break_minutes' => 60]);
        $dayShift->policy()->create();
        $nightShift = Shift::factory()->nightShift()->create(['unpaid_break_minutes' => 60]);
        $nightShift->policy()->create(['late_grace_period_minutes' => 20]);
        $workZone1 = WorkZone::factory()->create(['name' => 'Main Office']);
        $workZone2 = WorkZone::factory()->create(['name' => 'Warehouse A']);

        // Create 15 Users, then loop to create profiles and data
        User::factory(15)->create()->each(function ($user) use ($dayShift, $nightShift, $workZone1, $workZone2) {
            $profile = EmployeeProfile::factory()->create(['user_id' => $user->id]);
            $user->assignRole('Employee');

            $isDayWorker = $user->id % 2 != 0;
            $primaryShift = $isDayWorker ? $dayShift : $nightShift;
            $primaryZone = $isDayWorker ? $workZone1 : $workZone2;

            EmployeeShift::create([
                'employee_profile_id' => $profile->id,
                'shift_id' => $primaryShift->id,
                'work_zone_id' => $primaryZone->id,
                'start_date' => Carbon::parse('2025-01-01'),
                'end_date' => null,
            ]);

            $isSlacker = $user->id % 2 != 0;
            $startDate = now()->subMonths(6)->startOfMonth();
            $endDate = now()->subDay();

            for ($date = $startDate->copy(); $date->lt($endDate); $date->addDay()) {
                if ($date->isWeekend()) continue;

                // --- NEW REALISTIC VARIANCE LOGIC ---
                $roll = rand(1, 100); // Roll a 100-sided die for the day's event

                // Default to a normal day
                $checkInVariance = rand(-10, 10);
                $checkOutVariance = rand(-10, 20);

                if ($isSlacker) {
                    // Slackers have a high chance of being late/leaving early
                    if ($roll <= 60) { // 60% chance of a "slacker" day
                        $checkInVariance = rand(15, 60);
                        $checkOutVariance = rand(-90, 0);
                    } else if ($roll <= 80) { // 20% chance of being normal
                        // Use default values
                    } else { // 20% chance of a "good" day (overtime)
                        $checkInVariance = rand(-10, 0);
                        $checkOutVariance = rand(30, 90);
                    }
                } else { // Over-performer persona
                    // Over-performers have a high chance of staying late
                    if ($roll <= 70) { // 70% chance of a "good" day
                        $checkInVariance = rand(-20, 0);
                        $checkOutVariance = rand(30, 180);
                    } else if ($roll <= 90) { // 20% chance of a normal day
                        // Use default values
                    } else { // 10% chance of a "bad" day (late/undertime)
                        $checkInVariance = rand(15, 30);
                        $checkOutVariance = rand(-45, 0);
                    }
                }

                $this->createAttendanceRecord($profile, $primaryShift, $date, $checkInVariance, $checkOutVariance);
            }
        });
    }

    private function createAttendanceRecord($employee, $shift, $date, $checkInVariance, $checkOutVariance)
    {
        // This helper function is correct and does not need to be changed
        $policy = $shift->policy;
        $shiftStartTime = Carbon::parse($date->toDateString() . ' ' . $shift->start_time);
        $checkInTime = $shiftStartTime->copy()->addMinutes($checkInVariance);
        $shiftEndTime = Carbon::parse($date->toDateString() . ' ' . $shift->end_time);
        if ($shift->spans_two_days) { $shiftEndTime->addDay(); }
        $checkOutTime = $shiftEndTime->copy()->addMinutes($checkOutVariance);
        $totalMinutesOnClock = $checkInTime->diffInMinutes($checkOutTime);
        $scheduledMinutes = $shiftStartTime->diffInMinutes($shiftEndTime);
        $payableMinutes = $totalMinutesOnClock - $shift->unpaid_break_minutes;
        $netDifference = $payableMinutes - ($scheduledMinutes - $shift->unpaid_break_minutes);
        $overtime = 0;
        $undertime = 0;
        if ($netDifference > $policy->overtime_grace_period_minutes) { $overtime = $netDifference - $policy->overtime_grace_period_minutes; } 
        else if ($netDifference < -$policy->early_leave_grace_period_minutes) { $undertime = abs($netDifference); }
        $isLate = $checkInTime->gt($shiftStartTime->copy()->addMinutes($policy->late_grace_period_minutes));
        Attendance::create([
            'employee_profile_id' => $employee->id,
            'shift_id' => $shift->id,
            'check_in_time' => $checkInTime,
            'check_out_time' => $checkOutTime,
            'status' => $isLate ? 'late' : 'on_time',
            'overtime_minutes' => $overtime,
            'undertime_minutes' => $undertime,
            'check_in_latitude' => 0, 'check_in_longitude' => 0, 'check_in_selfie_path' => 'dummy.jpg',
        ]);
    }
}