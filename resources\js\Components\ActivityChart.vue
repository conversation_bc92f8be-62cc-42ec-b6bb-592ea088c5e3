<script setup>
import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';
import { computed } from 'vue';

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);

const props = defineProps({
    activityData: Object,
});

const chartData = computed(() => {
    const labels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const data = labels.map(day => props.activityData[day] || 0);
    return {
        labels,
        datasets: [{
            label: 'Hours Worked',
            backgroundColor: '#f87979', // You can change this color
            data: data,
        }],
    };
});

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
};
</script>

<template>
    <Bar :data="chartData" :options="chartOptions" />
</template>